import asyncio
import websockets
import json
import base64
import aiohttp

API_KEY = "sk-23c27f057aad4c52a7a778119edfb87e"
ENDPOINT = "wss://dashscope.aliyuncs.com/v1/real-time-recognition"

AUDIO_URL = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"

async def stream_audio(ws):
    async with aiohttp.ClientSession() as session:
        async with session.get(AUDIO_URL) as resp:
            while chunk := await resp.content.read(3200):  # 每读约 100ms PCM 数据
                msg = {"type": "audio", "audio": base64.b64encode(chunk).decode()}
                await ws.send(json.dumps(msg))
                await asyncio.sleep(0.1)
    await ws.send(json.dumps({"type": "end"}))

async def recv(ws):
    async for message in ws:
        resp = json.loads(message)
        if resp.get("type") == "partial":
            print("[中间识别] ", resp.get("text"))
        elif resp.get("type") == "final":
            print("[最终识别] ", resp.get("text"))

async def main():
    headers = {"Authorization": f"Bearer {API_KEY}"}
    async with websockets.connect(ENDPOINT, extra_headers=headers) as ws:
        await ws.send(json.dumps({
            "type": "start",
            "params": {
                "model": "paraformer-realtime-v2",
                "language_hints": ["zh"]
            }
        }))
        await asyncio.gather(stream_audio(ws), recv(ws))

if __name__ == "__main__":
    asyncio.run(main())
