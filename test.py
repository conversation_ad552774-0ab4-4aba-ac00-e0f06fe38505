import time, requests, json

API_KEY = "你的 API Key"
ENDPOINT = "https://dashscope.aliyuncs.com"
AUDIO_URL = "https://...mp3"

# 1. 提交识别任务
payload = {
  "model": "paraformer-v2",
  "input": {"file_urls": [AUDIO_URL]},
  "parameters": {"language_hints": ["zh"]}
}
headers = {
  "Authorization": f"Bearer {API_KEY}",
  "Content-Type": "application/json",
  "X-DashScope-Async": "enable"
}
resp = requests.post(f"{ENDPOINT}/api/v1/services/audio/asr/transcription", json=payload, headers=headers)
task_id = resp.json()["output"]["task_id"]

# 2. 轮询任务状态拿结果 URL
while True:
    status = requests.get(f"{ENDPOINT}/api/v1/tasks/{task_id}", headers=headers).json()
    if status["output"]["task_status"] == "SUCCEEDED":
        res_url = status["output"]["results"][0]["transcription_url"]
        break
    time.sleep(2)

# 3. 下载 JSON 识别结果
json_data = requests.get(res_url).json()
