import requests
import time
import json

API_KEY = "sk-23c27f057aad4c52a7a778119edfb87e"
ENDPOINT = "https://dashscope.aliyuncs.com"
AUDIO_URL = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"


def transcribe_audio():
    # 1. 提交识别任务
    payload = {
        "model": "paraformer-v2",
        "input": {"file_urls": [AUDIO_URL]},
        "parameters": {"language_hints": ["en"], "semantic_punctuation_enabled": 300}
    }
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "X-DashScope-Async": "enable"
    }

    print("正在提交识别任务...")
    resp = requests.post(
        f"{ENDPOINT}/api/v1/services/audio/asr/transcription", json=payload, headers=headers)

    if resp.status_code != 200:
        print(f"提交任务失败: {resp.status_code}")
        print(f"错误信息: {resp.text}")
        return

    result = resp.json()
    if "output" not in result or "task_id" not in result["output"]:
        print(f"提交任务失败: {result}")
        return

    task_id = result["output"]["task_id"]
    print(f"任务ID: {task_id}")

    # 2. 轮询任务状态拿结果 URL
    print("正在等待识别结果...")
    while True:
        status_resp = requests.get(
            f"{ENDPOINT}/api/v1/tasks/{task_id}", headers=headers)
        if status_resp.status_code != 200:
            print(f"查询状态失败: {status_resp.status_code}")
            print(f"错误信息: {status_resp.text}")
            return

        status = status_resp.json()
        print(f"任务状态: {status}")

        if "output" not in status:
            print("状态响应格式错误")
            return

        task_status = status["output"].get("task_status")
        if task_status == "SUCCEEDED":
            res_url = status["output"]["results"][0]["transcription_url"]
            print(f"识别完成，结果URL: {res_url}")
            break
        elif task_status == "FAILED":
            print("识别任务失败")
            return
        else:
            print(f"当前状态: {task_status}，等待中...")
            time.sleep(2)

    # 3. 下载 JSON 识别结果
    print("正在下载识别结果...")
    json_resp = requests.get(res_url)
    if json_resp.status_code != 200:
        print(f"下载结果失败: {json_resp.status_code}")
        return

    json_data = json_resp.json()
    print("识别结果:")
    print(json.dumps(json_data, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    transcribe_audio()
