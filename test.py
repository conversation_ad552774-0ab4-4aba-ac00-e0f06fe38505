import requests
import time
import json

API_KEY = "sk-23c27f057aad4c52a7a778119edfb87e"
ENDPOINT = "https://dashscope.aliyuncs.com"
AUDIO_URL = "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3"


def transcribe_audio():
    # 1. 提交识别任务
    payload = {
        "model": "paraformer-realtime-v2",
        "input": {"file_urls": [AUDIO_URL]},
        "parameters": {
            "language_hints": ["en"],
            "semantic_punctuation_enabled": True,   # 启用语义断句，更准确的分句
            "punctuation_prediction_enabled": True,
            "inverse_text_normalization_enabled": True
        }
    }
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "X-DashScope-Async": "enable"
    }

    print("正在提交识别任务...")
    resp = requests.post(
        f"{ENDPOINT}/api/v1/services/audio/asr/transcription", json=payload, headers=headers)

    if resp.status_code != 200:
        print(f"提交任务失败: {resp.status_code}")
        print(f"错误信息: {resp.text}")
        return

    result = resp.json()
    if "output" not in result or "task_id" not in result["output"]:
        print(f"提交任务失败: {result}")
        return

    task_id = result["output"]["task_id"]
    print(f"任务ID: {task_id}")

    # 2. 轮询任务状态拿结果 URL
    print("正在等待识别结果...")
    while True:
        status_resp = requests.get(
            f"{ENDPOINT}/api/v1/tasks/{task_id}", headers=headers)
        if status_resp.status_code != 200:
            print(f"查询状态失败: {status_resp.status_code}")
            print(f"错误信息: {status_resp.text}")
            return

        status = status_resp.json()
        print(f"任务状态: {status}")

        if "output" not in status:
            print("状态响应格式错误")
            return

        task_status = status["output"].get("task_status")
        if task_status == "SUCCEEDED":
            res_url = status["output"]["results"][0]["transcription_url"]
            print(f"识别完成，结果URL: {res_url}")
            break
        elif task_status == "FAILED":
            print("识别任务失败")
            return
        else:
            print(f"当前状态: {task_status}，等待中...")
            time.sleep(2)

    # 3. 下载 JSON 识别结果
    print("正在下载识别结果...")
    json_resp = requests.get(res_url)
    if json_resp.status_code != 200:
        print(f"下载结果失败: {json_resp.status_code}")
        return

    json_data = json_resp.json()

    # 保存完整结果到JSON文件
    output_file = "transcription_result.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)

    # 生成字幕格式文件
    subtitle_file = "subtitles.txt"
    generate_subtitles(json_data, subtitle_file)

    print(f"完整识别结果已保存到: {output_file}")
    print(f"字幕文件已保存到: {subtitle_file}")

    # 显示音频信息
    if "properties" in json_data:
        props = json_data["properties"]
        duration = props.get("original_duration_in_milliseconds", 0) / 1000
        format_info = props.get("audio_format", "unknown")
        sample_rate = props.get("original_sampling_rate", "unknown")
        print(f"音频时长: {duration:.2f}秒")
        print(f"音频格式: {format_info}")
        print(f"采样率: {sample_rate}Hz")


def generate_subtitles(json_data, output_file):
    """生成字幕格式文件"""
    subtitles = []

    if "transcripts" in json_data and len(json_data["transcripts"]) > 0:
        transcript = json_data["transcripts"][0]

        if "sentences" in transcript:
            subtitle_index = 1
            for sentence in transcript["sentences"]:
                start_time = sentence.get("begin_time", 0) / 1000  # 转换为秒
                end_time = sentence.get("end_time", 0) / 1000 if sentence.get("end_time") else start_time + 3
                text = sentence.get("text", "").strip()

                if text:  # 只处理非空文本
                    print(f"处理句子: {text[:50]}...")  # 调试信息
                    # 如果句子太长，按标点符号和长度智能分割
                    text_segments = smart_split_text(text, max_length=80)
                    print(f"分割成 {len(text_segments)} 段")  # 调试信息

                    # 计算每个分段的时间
                    segment_duration = (end_time - start_time) / len(text_segments)

                    for i, segment in enumerate(text_segments):
                        segment_start = start_time + i * segment_duration
                        segment_end = start_time + (i + 1) * segment_duration

                        # 格式化时间戳
                        start_formatted = format_timestamp(segment_start)
                        end_formatted = format_timestamp(segment_end)

                        # 添加字幕条目
                        subtitle_entry = f"{subtitle_index}\n{start_formatted} --> {end_formatted}\n{segment}\n"
                        subtitles.append(subtitle_entry)
                        subtitle_index += 1

    # 保存字幕文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("\n".join(subtitles))

    # 显示字幕预览
    print("\n=== 字幕预览 ===")
    for i, subtitle in enumerate(subtitles[:5]):  # 只显示前5条
        print(subtitle)
        if i < 4 and i < len(subtitles) - 1:
            print("---")

    if len(subtitles) > 5:
        print(f"... 还有 {len(subtitles) - 5} 条字幕")


def smart_split_text(text, max_length=80):
    """智能分割文本为适合字幕的长度"""
    if len(text) <= max_length:
        return [text]

    # 按标点符号分割
    import re
    sentences = re.split(r'([.!?;,])', text)

    segments = []
    current_segment = ""

    for i in range(0, len(sentences), 2):
        sentence = sentences[i] if i < len(sentences) else ""
        punctuation = sentences[i + 1] if i + 1 < len(sentences) else ""

        full_sentence = sentence + punctuation

        # 如果当前段落加上新句子不超过最大长度
        if len(current_segment + full_sentence) <= max_length:
            current_segment += full_sentence
        else:
            # 如果当前段落不为空，先保存
            if current_segment.strip():
                segments.append(current_segment.strip())

            # 如果单个句子太长，按单词分割
            if len(full_sentence) > max_length:
                words = full_sentence.split()
                temp_segment = ""
                for word in words:
                    if len(temp_segment + " " + word) <= max_length:
                        temp_segment += (" " + word) if temp_segment else word
                    else:
                        if temp_segment:
                            segments.append(temp_segment)
                        temp_segment = word
                if temp_segment:
                    current_segment = temp_segment
            else:
                current_segment = full_sentence

    # 添加最后一个段落
    if current_segment.strip():
        segments.append(current_segment.strip())

    return segments


def format_timestamp(seconds):
    """将秒数转换为字幕时间格式 HH:MM:SS,mmm"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"


if __name__ == "__main__":
    transcribe_audio()
